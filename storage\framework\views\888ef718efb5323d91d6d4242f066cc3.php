<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Plans'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12">
                <div class="card mb-4">
                    <div class="d-flex justify-content-between px-3 py-4">
                        <h6 class="mb-0" style="color: #67748e;">Plans</h6>
                        <a href="<?php echo e(route('plan.create')); ?>" class="btn btn-sm m-0" style="background-color: #67748e; border-color: #67748e; color: white;">
                            <i class="fa fa-plus me-1"></i> Add Plan
                        </a>
                    </div>

                    <div class="card-body p-3">
                        <?php if (isset($component)) { $__componentOriginalc8463834ba515134d5c98b88e1a9dc03 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.data-table','data' => ['id' => 'plansTable','ajax' => route('plan.index'),'columns' => [
                            [
                                'data' => 'DT_RowIndex',
                                'name' => 'DT_RowIndex',
                                'orderable' => false,
                                'searchable' => false,
                            ],
                            ['data' => 'name', 'name' => 'name'],
                            ['data' => 'price', 'name' => 'price'],
                            ['data' => 'duration', 'name' => 'duration'],
                            ['data' => 'sort_order', 'name' => 'sort_order'],
                            ['data' => 'description', 'name' => 'description'],
                            ['data' => 'listed_services', 'name' => 'listed_services', 'orderable' => false, 'searchable' => false],
                            ['data' => 'status', 'name' => 'status'],
                            ['data' => 'is_popular', 'name' => 'is_popular'],
                            ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                        ],'order' => []]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('data-table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'plansTable','ajax' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('plan.index')),'columns' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
                            [
                                'data' => 'DT_RowIndex',
                                'name' => 'DT_RowIndex',
                                'orderable' => false,
                                'searchable' => false,
                            ],
                            ['data' => 'name', 'name' => 'name'],
                            ['data' => 'price', 'name' => 'price'],
                            ['data' => 'duration', 'name' => 'duration'],
                            ['data' => 'sort_order', 'name' => 'sort_order'],
                            ['data' => 'description', 'name' => 'description'],
                            ['data' => 'listed_services', 'name' => 'listed_services', 'orderable' => false, 'searchable' => false],
                            ['data' => 'status', 'name' => 'status'],
                            ['data' => 'is_popular', 'name' => 'is_popular'],
                            ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                        ]),'order' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([])]); ?>
                             <?php $__env->slot('header', null, []); ?> 
                                <th>S.No.</th>
                                <th>Name</th>
                                <th>Price</th>
                                <th>Duration</th>
                                <th>Sort Order</th>
                                <th>Description</th>
                                <th>Listed Services</th>
                                <th>Status</th>
                                <th>Popular</th>
                                <th>Actions</th>
                             <?php $__env->endSlot(); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $attributes = $__attributesOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__attributesOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03)): ?>
<?php $component = $__componentOriginalc8463834ba515134d5c98b88e1a9dc03; ?>
<?php unset($__componentOriginalc8463834ba515134d5c98b88e1a9dc03); ?>
<?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Delete Plan
            $(document).on('click', '.delete-button', function() {
                const id = $(this).data('id');

                Swal.fire({
                    title: 'Are you sure?',
                    text: "This will permanently delete the plan.",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!',
                    scrollbarPadding: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: '<?php echo e(route('plan.destroy', ':id')); ?>'.replace(':id', id),
                            type: 'DELETE',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(response) {
                                $('#plansTable').DataTable().ajax.reload(null, false);
                                Swal.fire({
                                    title: 'Deleted!',
                                    text: 'The plan has been deleted.',
                                    icon: 'success',
                                    timer: 2000,
                                    showConfirmButton: false,
                                    scrollbarPadding: false
                                });
                            },
                            error: function() {
                                Swal.fire({
                                    title: 'Error!',
                                    text: 'Something went wrong.',
                                    icon: 'error',
                                    timer: 2000,
                                    showConfirmButton: false,
                                    scrollbarPadding: false
                                });
                            }
                        });
                    }
                });
            });

            // Toggle Plan Status
            $(document).on('click', '.toggle-status', function() {
                const btn = $(this);
                const id = btn.data('id');
                const currentStatus = btn.text().trim();
                const newStatus = currentStatus === 'Active' ? 'Inactive' : 'Active';

                Swal.fire({
                    title: `Change status to ${newStatus}?`,
                    text: "Are you sure you want to toggle the status?",
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#aaa',
                    confirmButtonText: `Yes, make it ${newStatus}`,
                    cancelButtonText: 'Cancel',
                    scrollbarPadding: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: `<?php echo e(route('plan.status', ':id')); ?>`.replace(':id', id),
                            type: 'POST',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(res) {
                                if (res.status) {
                                    // Reload the DataTable to reflect changes
                                    $('#plansTable').DataTable().ajax.reload(null, false);

                                    Swal.fire({
                                        title: 'Updated!',
                                        text: `Status changed to ${res.newStatus}.`,
                                        icon: 'success',
                                        timer: 2000,
                                        showConfirmButton: false,
                                        scrollbarPadding: false
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'Error!',
                                        text: res.error || 'Failed to update status.',
                                        icon: 'error',
                                        timer: 2000,
                                        showConfirmButton: false,
                                        scrollbarPadding: false
                                    });
                                }
                            },
                            error: function(xhr) {
                                const errorMsg = xhr.responseJSON?.error || 'Failed to update status.';
                                Swal.fire({
                                    title: 'Error!',
                                    text: errorMsg,
                                    icon: 'error',
                                    timer: 2000,
                                    showConfirmButton: false,
                                    scrollbarPadding: false
                                });
                            }
                        });
                    }
                });
            });

            // Toggle Plan Popular Status
            $(document).on('click', '.toggle-popular', function() {
                const btn = $(this);
                const id = btn.data('id');
                const currentPopular = btn.text().trim();
                const newPopular = currentPopular === 'Popular' ? 'Regular' : 'Popular';

                Swal.fire({
                    title: `Change to ${newPopular}?`,
                    text: "Are you sure you want to toggle the popular status?",
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#aaa',
                    confirmButtonText: `Yes, make it ${newPopular}`,
                    cancelButtonText: 'Cancel',
                    scrollbarPadding: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: `<?php echo e(route('plan.popular', ':id')); ?>`.replace(':id', id),
                            type: 'POST',
                            data: {
                                _token: '<?php echo e(csrf_token()); ?>'
                            },
                            success: function(res) {
                                if (res.status) {
                                    // Reload the DataTable to reflect changes
                                    $('#plansTable').DataTable().ajax.reload(null, false);

                                    Swal.fire({
                                        title: 'Updated!',
                                        text: `Popular status changed to ${res.newPopular}.`,
                                        icon: 'success',
                                        timer: 2000,
                                        showConfirmButton: false,
                                        scrollbarPadding: false
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'Error!',
                                        text: res.error || 'Failed to update popular status.',
                                        icon: 'error',
                                        timer: 2000,
                                        showConfirmButton: false,
                                        scrollbarPadding: false
                                    });
                                }
                            },
                            error: function(xhr) {
                                const errorMsg = xhr.responseJSON?.error || 'Failed to update popular status.';
                                Swal.fire({
                                    title: 'Error!',
                                    text: errorMsg,
                                    icon: 'error',
                                    timer: 2000,
                                    showConfirmButton: false,
                                    scrollbarPadding: false
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/plan/index.blade.php ENDPATH**/ ?>