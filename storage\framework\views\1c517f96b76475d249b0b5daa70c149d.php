<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Edit Plan'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header pb-0 d-flex justify-content-between align-items-center">
                        <h6 style="color: #67748e;">Edit Plan: <?php echo e($plan->name); ?></h6>
                        <a href="<?php echo e(route('plan.index')); ?>" class="btn btn-sm" style="background-color: #8392ab; border-color: #8392ab; color: white;">
                            <i class="fas fa-arrow-left me-1"></i> Back to Plans
                        </a>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(route('plan.update', $plan->plans_id)); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('PUT'); ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="name" class="form-control-label">Plan Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="name" name="name" value="<?php echo e(old('name', $plan->name)); ?>" 
                                               placeholder="Enter plan name" required>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="price" class="form-control-label">Price <span class="text-danger">*</span></label>
                                        <input type="number" step="0.01" min="0" 
                                               class="form-control <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="price" name="price" value="<?php echo e(old('price', $plan->price)); ?>" 
                                               placeholder="Enter price" required>
                                        <?php $__errorArgs = ['price'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="duration" class="form-control-label">Duration <span class="text-danger">*</span></label>
                                        <select class="form-control <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="duration" name="duration" required>
                                            <option value="">Select Duration</option>
                                            <option value="weekly" <?php echo e(old('duration', $plan->duration) == 'weekly' ? 'selected' : ''); ?>>Weekly</option>
                                            <option value="monthly" <?php echo e(old('duration', $plan->duration) == 'monthly' ? 'selected' : ''); ?>>Monthly</option>
                                            <option value="yearly" <?php echo e(old('duration', $plan->duration) == 'yearly' ? 'selected' : ''); ?>>Yearly</option>
                                        </select>
                                        <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="sort_order" class="form-control-label">Sort Order</label>
                                        <input type="number" min="0" 
                                               class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $plan->sort_order)); ?>" 
                                               placeholder="Enter sort order">
                                        <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="description" class="form-control-label">Description</label>
                                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                  id="description" name="description" rows="4" 
                                                  placeholder="Enter plan description"><?php echo e(old('description', $plan->description)); ?></textarea>
                                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-control-label">Services/Features</label>
                                        <div id="features-container" class="features-wrapper">
                                            <?php
                                                // Get existing services for this plan
                                                $existingServices = $plan->services->pluck('name')->toArray();
                                                $features = old('features', $existingServices);
                                            ?>
                                            <?php if(!empty($features)): ?>
                                                <?php $__currentLoopData = $features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="feature-item mb-3">
                                                        <div class="d-flex align-items-center">
                                                            <div class="flex-grow-1 me-2">
                                                                <input type="text" class="form-control" name="features[]"
                                                                       value="<?php echo e($feature); ?>" placeholder="Enter service/feature">
                                                            </div>
                                                            <button type="button" class="btn btn-outline-danger btn-sm remove-feature" title="Remove Feature">
                                                                <i class="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php else: ?>
                                                <div class="feature-item mb-3">
                                                    <div class="d-flex align-items-center">
                                                        <div class="flex-grow-1 me-2">
                                                            <input type="text" class="form-control" name="features[]"
                                                                   placeholder="Enter service/feature">
                                                        </div>
                                                        <button type="button" class="btn btn-outline-danger btn-sm remove-feature" title="Remove Feature">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-outline-primary btn-sm" id="add-feature">
                                                <i class="fas fa-plus me-1"></i> Add Service/Feature
                                            </button>
                                            <div class="mt-2">
                                                <small class="form-text text-muted">Each service/feature will be stored separately and linked to this plan</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="status" class="form-control-label">Status <span class="text-danger">*</span></label>
                                        <select class="form-control <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="status" name="status" required>
                                            <option value="1" <?php echo e(old('status', $plan->status) == '1' ? 'selected' : ''); ?>>Active</option>
                                            <option value="0" <?php echo e(old('status', $plan->status) == '0' ? 'selected' : ''); ?>>Inactive</option>
                                        </select>
                                        <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="is_popular" class="form-control-label">Popular Plan</label>
                                        <select class="form-control <?php $__errorArgs = ['is_popular'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                id="is_popular" name="is_popular">
                                            <option value="0" <?php echo e(old('is_popular', $plan->is_popular) == '0' ? 'selected' : ''); ?>>No</option>
                                            <option value="1" <?php echo e(old('is_popular', $plan->is_popular) == '1' ? 'selected' : ''); ?>>Yes</option>
                                        </select>
                                        <?php $__errorArgs = ['is_popular'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group text-end">
                                        <button type="submit" class="btn" style="background-color: #67748e; border-color: #67748e; color: white;">
                                            <i class="fas fa-save me-1"></i> Update Plan
                                        </button>
                                        <a href="<?php echo e(route('plan.index')); ?>" class="btn ms-2" style="background-color: #8392ab; border-color: #8392ab; color: white;">
                                            <i class="fas fa-times me-1"></i> Cancel
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css'); ?>
<style>
.features-wrapper {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.feature-item {
    background: white;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.feature-item:hover {
    border-color: #67748e;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.feature-item .form-control {
    border: none;
    background: transparent;
    padding: 8px 12px;
}

.feature-item .form-control:focus {
    box-shadow: none;
    background: transparent;
}

.remove-feature {
    min-width: 36px;
    height: 36px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-feature:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

#add-feature {
    border-radius: 6px;
    padding: 8px 16px;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function() {
    // Add new feature input
    $('#add-feature').click(function() {
        var featureHtml = `
            <div class="feature-item mb-3">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1 me-2">
                        <input type="text" class="form-control" name="features[]" placeholder="Enter service/feature">
                    </div>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-feature" title="Remove Feature">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        $('#features-container').append(featureHtml);
    });

    // Remove feature input
    $(document).on('click', '.remove-feature', function() {
        if ($('.feature-item').length > 1) {
            $(this).closest('.feature-item').remove();
        } else {
            toastr.warning('At least one feature field is required');
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/plan/edit.blade.php ENDPATH**/ ?>