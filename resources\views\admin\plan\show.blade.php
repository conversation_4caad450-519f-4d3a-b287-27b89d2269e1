@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Plan Details'])

    <div class="container-fluid py-4">
        <!-- Plan Header Card -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-3">
                        <h4 class="mb-2">{{ $plan->name }}</h4>
                        <div class="mb-2">
                            <span class="h5 text-primary">${{ number_format($plan->price, 2) }}</span>
                            <span class="text-muted">/ {{ $plan->duration }}</span>
                        </div>
                        <div class="mb-3">
                            @if($plan->status)
                                <span class="badge bg-success me-2">Active</span>
                            @else
                                <span class="badge bg-secondary me-2">Inactive</span>
                            @endif

                            @if($plan->is_popular)
                                <span class="badge bg-warning">Popular</span>
                            @endif
                        </div>

                        <div class="d-flex justify-content-center gap-2">
                            <a href="{{ route('plan.edit', $plan->plans_id) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit me-1"></i> Edit
                            </a>
                            <button type="button" class="btn btn-danger btn-sm" onclick="deletePlan('{{ $plan->plans_id }}')">
                                <i class="fas fa-trash me-1"></i> Delete
                            </button>
                            <a href="{{ route('plan.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i> Back
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Plan Details Cards -->
        <div class="row">
            <!-- Basic Information Card -->
            <div class="col-lg-8">
                <div class="card info-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2 text-primary"></i>
                            Basic Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-tag text-primary me-2"></i>
                                    <label class="form-control-label fw-bold mb-0">Plan Name</label>
                                </div>
                                <p class="text-muted mb-0 ms-4">{{ $plan->name }}</p>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-dollar-sign text-success me-2"></i>
                                    <label class="form-control-label fw-bold mb-0">Price</label>
                                </div>
                                <p class="text-muted mb-0 ms-4">${{ number_format($plan->price, 2) }}</p>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-clock text-info me-2"></i>
                                    <label class="form-control-label fw-bold mb-0">Duration</label>
                                </div>
                                <p class="text-muted mb-0 ms-4">{{ ucfirst($plan->duration) }}</p>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-sort-numeric-up text-warning me-2"></i>
                                    <label class="form-control-label fw-bold mb-0">Sort Order</label>
                                </div>
                                <p class="text-muted mb-0 ms-4">{{ $plan->sort_order }}</p>
                            </div>
                        </div>

                        @if($plan->description)
                        <div class="description-section mt-4">
                            <div class="section-header">
                                <i class="fas fa-align-left me-2 text-primary"></i>
                                <h6 class="mb-0">Description</h6>
                            </div>
                            <div class="description-content">
                                <p class="mb-0">{{ $plan->description }}</p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Statistics & Meta Card -->
            <div class="col-lg-4">
                <div class="card stats-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2 text-success"></i>
                            Statistics & Meta
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-calendar-plus text-primary me-2"></i>
                                <label class="form-control-label fw-bold mb-0">Created At</label>
                            </div>
                            <p class="text-muted mb-0 ms-4">{{ $plan->created_at->format('d M, Y') }}</p>
                            <small class="text-muted ms-4">{{ $plan->created_at->format('h:i A') }}</small>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-edit text-warning me-2"></i>
                                <label class="form-control-label fw-bold mb-0">Last Updated</label>
                            </div>
                            <p class="text-muted mb-0 ms-4">{{ $plan->updated_at->format('d M, Y') }}</p>
                            <small class="text-muted ms-4">{{ $plan->updated_at->format('h:i A') }}</small>
                        </div>

                        <div class="mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-users text-info me-2"></i>
                                <label class="form-control-label fw-bold mb-0">Total Subscribers</label>
                            </div>
                            <p class="text-muted mb-0 ms-4">{{ $plan->subscribers_count ?? 0 }}</p>
                            <small class="text-muted ms-4">Active subscriptions</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Card -->
        @if($plan->services && count($plan->services) > 0)
        <div class="row">
            <div class="col-12">
                <div class="card features-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list-check me-2 text-success"></i>
                            Services & Features
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="features-list">
                            @foreach($plan->services as $service)
                                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                    <span class="feature-text">{{ $service->name }}</span>
                                    <i class="fas fa-check-circle text-success"></i>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
@endsection

@push('js')
<script>
function deletePlan(planId) {
    if (confirm('Are you sure you want to delete this plan?')) {
        $.ajax({
            url: "{{ route('plan.destroy', ':id') }}".replace(':id', planId),
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.success);
                    setTimeout(function() {
                        window.location.href = "{{ route('plan.index') }}";
                    }, 1500);
                } else {
                    toastr.error(response.error || 'Something went wrong');
                }
            },
            error: function(xhr) {
                var errorMsg = xhr.responseJSON?.error || 'Something went wrong';
                toastr.error(errorMsg);
            }
        });
    }
}
</script>
@endpush
