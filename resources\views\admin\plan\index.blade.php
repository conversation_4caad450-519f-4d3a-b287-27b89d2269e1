@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Plans'])

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12">
                <div class="card mb-4">
                    <div class="d-flex justify-content-between px-3 py-4">
                        <h6 class="mb-0" style="color: #67748e;">Plans</h6>
                        <a href="{{ route('plan.create') }}" class="btn btn-sm m-0" style="background-color: #67748e; border-color: #67748e; color: white;">
                            <i class="fa fa-plus me-1"></i> Add Plan
                        </a>
                    </div>

                    <div class="card-body p-3">
                        <x-data-table id="plansTable" :ajax="route('plan.index')" :columns="[
                            [
                                'data' => 'DT_RowIndex',
                                'name' => 'DT_RowIndex',
                                'orderable' => false,
                                'searchable' => false,
                            ],
                            ['data' => 'name', 'name' => 'name'],
                            ['data' => 'price', 'name' => 'price'],
                            ['data' => 'duration', 'name' => 'duration'],
                            ['data' => 'sort_order', 'name' => 'sort_order'],
                            ['data' => 'description', 'name' => 'description'],
                            ['data' => 'listed_services', 'name' => 'listed_services', 'orderable' => false, 'searchable' => false],
                            ['data' => 'status', 'name' => 'status'],
                            ['data' => 'is_popular', 'name' => 'is_popular'],
                            ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                        ]" :order="[]">
                            <x-slot:header>
                                <th>S.No.</th>
                                <th>Name</th>
                                <th>Price</th>
                                <th>Duration</th>
                                <th>Sort Order</th>
                                <th>Description</th>
                                <th>Listed Services</th>
                                <th>Status</th>
                                <th>Popular</th>
                                <th>Actions</th>
                            </x-slot:header>
                        </x-data-table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Delete Plan
            $(document).on('click', '.delete-button', function() {
                const id = $(this).data('id');

                Swal.fire({
                    title: 'Are you sure?',
                    text: "This will permanently delete the plan.",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, delete it!',
                    scrollbarPadding: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: '{{ route('plan.destroy', ':id') }}'.replace(':id', id),
                            type: 'DELETE',
                            data: {
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                $('#plansTable').DataTable().ajax.reload(null, false);
                                Swal.fire({
                                    title: 'Deleted!',
                                    text: 'The plan has been deleted.',
                                    icon: 'success',
                                    timer: 2000,
                                    showConfirmButton: false,
                                    scrollbarPadding: false
                                });
                            },
                            error: function() {
                                Swal.fire({
                                    title: 'Error!',
                                    text: 'Something went wrong.',
                                    icon: 'error',
                                    timer: 2000,
                                    showConfirmButton: false,
                                    scrollbarPadding: false
                                });
                            }
                        });
                    }
                });
            });

            // Toggle Plan Status
            $(document).on('click', '.toggle-status', function() {
                const btn = $(this);
                const id = btn.data('id');
                const currentStatus = btn.text().trim();
                const newStatus = currentStatus === 'Active' ? 'Inactive' : 'Active';

                Swal.fire({
                    title: `Change status to ${newStatus}?`,
                    text: "Are you sure you want to toggle the status?",
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#aaa',
                    confirmButtonText: `Yes, make it ${newStatus}`,
                    cancelButtonText: 'Cancel',
                    scrollbarPadding: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.post(`{{ route('plan.status', ':id') }}`.replace(':id', id), {
                            _token: '{{ csrf_token() }}'
                        }, function(res) {
                            if (res.status) {
                                btn.toggleClass('btn-success btn-secondary');
                                btn.text(res.newStatus);

                                Swal.fire({
                                    title: 'Updated!',
                                    text: `Status changed to ${res.newStatus}.`,
                                    icon: 'success',
                                    timer: 2000,
                                    showConfirmButton: false,
                                    scrollbarPadding: false
                                });
                            }
                        }).fail(function() {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Failed to update status.',
                                icon: 'error',
                                timer: 2000,
                                showConfirmButton: false,
                                scrollbarPadding: false
                            });
                        });
                    }
                });
            });
        });
    </script>
@endsection
