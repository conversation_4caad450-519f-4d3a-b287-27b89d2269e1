<?php

namespace App\Http\Controllers\Admin;

use Exception;
use App\Models\Plan;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Service;
use Yajra\DataTables\Facades\DataTables;

class PlanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        try {
            if ($request->ajax()) {
                $plans = Plan::with('services')
                    ->select('plans_id', 'name', 'description', 'price', 'duration', 'status', 'is_popular', 'sort_order', 'created_at')
                    ->orderBy('sort_order', 'asc')
                    ->orderByDesc('created_at')
                    ->get();

                return DataTables::of($plans)
                    ->addIndexColumn()

                    ->editColumn('name', fn($p) => ucwords($p->name ?? '-'))

                    ->editColumn('description', fn($p) => Str::limit($p->description ?? '-', 50))

                    ->editColumn('price', fn($p) => '$' . number_format($p->price, 2))

                    ->editColumn('duration', fn($p) => ucfirst($p->duration ?? '-'))

                    ->editColumn('sort_order', fn($p) => $p->sort_order ?? 0)

                    ->editColumn('listed_services', function ($p) {
                        $services = $p->services->pluck('name')->toArray();
                        if (empty($services)) {
                            return '<span class="text-muted">No services</span>';
                        }
                        $servicesList = implode(', ', array_slice($services, 0, 3));
                        if (count($services) > 3) {
                            $servicesList .= ' <small class="text-muted">+' . (count($services) - 3) . ' more</small>';
                        }
                        return $servicesList;
                    })

                    ->editColumn('status', function ($p) {
                        $status = $p->status ? 'Active' : 'Inactive';
                        $btnClass = $p->status ? 'btn-success' : 'btn-secondary';

                        return '<button type="button" class="m-0 bdr btn btn-sm toggle-status ' . $btnClass . '" data-id="' . e($p->plans_id) . '">' . $status . '</button>';
                    })

                    ->editColumn('is_popular', function ($p) {
                        $popular = $p->is_popular ? 'Popular' : 'Regular';
                        $btnClass = $p->is_popular ? 'btn-warning' : 'btn-light';
                        $btnStyle = $p->is_popular
                            ? 'background-color: #ffc107; border-color: #ffc107; color: #000;'
                            : 'background-color: #8392ab; border-color: #8392ab; color: white;';

                        return '<button type="button" class="btn btn-sm toggle-popular ' . $btnClass . '" data-id="' . e($p->plans_id) . '" style="' . $btnStyle . '">' . $popular . '</button>';
                    })

                    ->editColumn('created_at', fn($p) => optional($p->created_at)->format('d M, Y h:i A'))

                    ->addColumn('actions', function ($p) {
                        $id = e($p->plans_id);
                        $editUrl = route('plan.edit', $id);
                        $showUrl = route('plan.show', $id);

                        return '
                    <div class="d-flex gap-2">
                        <a href="' . $showUrl . '" class="text-info ttt" title="View"><i class="fas fa-eye"></i></a>
                        <a href="' . $editUrl . '" class="text-primary ttt" title="Edit"><i class="fas fa-edit"></i></a>
                        <button type="button" class="text-danger border-0 bg-transparent p-0 m-0 delete-button ttt" data-id="' . $id . '" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>';
                    })

                    ->rawColumns(['status', 'is_popular', 'listed_services', 'actions'])
                    ->make(true);
            }

            return view('admin.plan.index');
        } catch (Exception $e) {
            Log::error('Error fetching plan data: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while loading the plan data.');
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.plan.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|min:3',
                'description' => 'nullable|string|max:1000',
                'price' => 'required|numeric|min:0',
                'duration' => 'required|in:weekly,monthly,yearly',
                'features' => 'nullable|array',
                'features.*' => 'string|max:255',
                'status' => 'required|in:0,1',
                'is_popular' => 'required|in:0,1',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            $plan = new Plan([
                'plans_id' => (string) Str::uuid(),
                'name' => $validated['name'],
                'description' => $validated['description'],
                'price' => $validated['price'],
                'duration' => $validated['duration'],
                'features' => $validated['features'] ?? [],
                'status' => $validated['status'] ?? 1,
                'is_popular' => $validated['is_popular'] ?? 0,
                'sort_order' => $validated['sort_order'] ?? 0,
            ]);

            $plan->save();

            // Create services from features
            if (!empty($validated['features'])) {
                foreach ($validated['features'] as $index => $feature) {
                    if (!empty(trim($feature))) {
                        Service::create([
                            'plan_id' => $plan->plans_id,
                            'name' => trim($feature),
                            'sort_order' => $index + 1,
                            'status' => 1,
                        ]);
                    }
                }
            }

            return redirect()->route('plan.index')->with('success', 'Plan created successfully!');
        } catch (Exception $e) {
            Log::error('Plan store error: ' . $e->getMessage());
            return redirect()->back()->withErrors(['error' => 'Failed to create plan. Please try again.'])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $plan = Plan::where('plans_id', $id)->firstOrFail();
        return view('admin.plan.show', compact('plan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $plan = Plan::with('services')->where('plans_id', $id)->firstOrFail();
        return view('admin.plan.edit', compact('plan'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $plan = Plan::where('plans_id', $id)->firstOrFail();

            $validated = $request->validate([
                'name' => 'required|string|max:255|min:3',
                'description' => 'nullable|string|max:1000',
                'price' => 'required|numeric|min:0',
                'duration' => 'required|in:weekly,monthly,yearly',
                'features' => 'nullable|array',
                'features.*' => 'string|max:255',
                'status' => 'required|in:0,1',
                'is_popular' => 'required|in:0,1',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            $plan->update([
                'name' => $validated['name'],
                'description' => $validated['description'],
                'price' => $validated['price'],
                'duration' => $validated['duration'],
                'features' => $validated['features'] ?? [],
                'status' => $validated['status'],
                'is_popular' => $validated['is_popular'],
                'sort_order' => $validated['sort_order'] ?? 0,
            ]);

            // Update services from features
            // First, delete existing services for this plan
            $plan->services()->delete();

            // Create new services from features
            if (!empty($validated['features'])) {
                foreach ($validated['features'] as $index => $feature) {
                    if (!empty(trim($feature))) {
                        Service::create([
                            'plan_id' => $plan->plans_id,
                            'name' => trim($feature),
                            'sort_order' => $index + 1,
                            'status' => 1,
                        ]);
                    }
                }
            }

            return redirect()->route('plan.index')->with('success', 'Plan updated successfully!');
        } catch (Exception $e) {
            Log::error('Plan update error: ' . $e->getMessage());
            return redirect()->back()->withErrors(['error' => 'Failed to update plan. Please try again.'])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $plan = Plan::where('plans_id', $id)->firstOrFail();
            $plan->delete();

            return response()->json(['success' => 'Plan deleted successfully!']);
        } catch (Exception $e) {
            Log::error('Plan delete error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to delete plan. Please try again.'], 500);
        }
    }

    /**
     * Toggle plan status
     */
    public function status(string $id)
    {
        try {
            $plan = Plan::where('plans_id', $id)->firstOrFail();
            $plan->status = !$plan->status;
            $plan->save();

            return response()->json([
                'status' => true,
                'newStatus' => $plan->status ? 'Active' : 'Inactive',
                'success' => 'Plan status updated successfully!'
            ]);
        } catch (Exception $e) {
            Log::error('Plan status error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'error' => 'Failed to update plan status. Please try again.'
            ], 500);
        }
    }

    /**
     * Toggle plan popular status
     */
    public function popular(string $id)
    {
        try {
            $plan = Plan::where('plans_id', $id)->firstOrFail();
            $plan->is_popular = !$plan->is_popular;
            $plan->save();

            return response()->json([
                'status' => true,
                'newPopular' => $plan->is_popular ? 'Popular' : 'Regular',
                'success' => 'Plan popular status updated successfully!'
            ]);
        } catch (Exception $e) {
            Log::error('Plan popular error: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'error' => 'Failed to update plan popular status. Please try again.'
            ], 500);
        }
    }
}
