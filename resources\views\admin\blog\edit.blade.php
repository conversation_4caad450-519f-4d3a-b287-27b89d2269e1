@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Blog / Edit'])

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 ttl">Edit Blog</h5>
                        <a href="{{ route('blog.index') }}" class="btn btn-sm m-0 bdr">
                            <i class="fa fa-arrow-left me-1"></i> Back
                        </a>
                    </div>

                    <div class="card-body">
                        <form action="{{ route('blog.update', $blog->blogs_id) }}" method="POST"
                            enctype="multipart/form-data">
                            @csrf
                            @method('PUT')

                            <div class="row g-4">
                                <!-- Title -->
                                <div class="col-12 col-md-6">
                                    <label class="form-label text-muted">Title <span class="text-danger">*</span></label>
                                    <input type="text" name="name"
                                        class="form-control @error('name') is-invalid @enderror"
                                        value="{{ old('name', $blog->name) }}">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Feature Image -->
                                <div class="col-12 col-md-6">
                                    <label class="form-label text-muted">Feature Image <span
                                            class="text-danger">*</span></label>
                                    <input type="file" name="feature_image" id="feature_image"
                                        class="form-control @error('feature_image') is-invalid @enderror">
                                    <div class="position-relative mt-2">
                                        <img id="featureImagePreview" src="{{ asset($blog->feature_image) }}"
                                            class="img-thumbnail {{ empty($blog->feature_image) ? 'd-none' : '' }}"
                                            style="max-height: 100px;">
                                        <button type="button" class="btn-close position-absolute top-0 end-0 m-1 d-none"
                                            aria-label="Remove" id="remove_feature_image"></button>
                                    </div>
                                    @error('feature_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Additional Image -->
                                <div class="col-12 col-md-6">
                                    <label class="form-label text-muted">Additional Image</label>
                                    <input type="file" name="image" id="image"
                                        class="form-control @error('image') is-invalid @enderror">
                                    <div class="position-relative mt-2">
                                        <img id="additionalImagePreview" src="{{ asset($blog->image) }}"
                                            class="img-thumbnail {{ empty($blog->image) ? 'd-none' : '' }}"
                                            style="max-height: 100px;">
                                        <button type="button" class="btn-close position-absolute top-0 end-0 m-1 d-none"
                                            aria-label="Remove" id="remove_image"></button>
                                    </div>
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Author Name -->
                                <div class="col-12 col-md-6">
                                    <label class="form-label text-muted">Author Name</label>
                                    <input type="text" name="author_name"
                                        class="form-control @error('author_name') is-invalid @enderror"
                                        value="{{ old('author_name', $blog->author_name) }}">
                                    @error('author_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Author Image -->
                                <div class="col-12 col-md-6">
                                    <label class="form-label text-muted">Author Image</label>
                                    <input type="file" name="author_image" id="author_image"
                                        class="form-control @error('author_image') is-invalid @enderror">
                                    <div class="position-relative mt-2">
                                        <img id="authorImagePreview" src="{{ asset($blog->author_image) }}"
                                            class="img-thumbnail {{ empty($blog->author_image) ? 'd-none' : '' }}"
                                            style="max-height: 100px;">
                                        <button type="button" class="btn-close position-absolute top-0 end-0 m-1 d-none"
                                            aria-label="Remove" id="remove_author_image"></button>
                                    </div>
                                    @error('author_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Description -->
                                <div class="col-12">
                                    <label class="form-label text-muted">Description <span
                                            class="text-danger">*</span></label>
                                    <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror">{{ old('description', $blog->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Publish Date -->
                                <div class="col-12 col-md-6">
                                    <label class="form-label text-muted">Publish Date</label>
                                    <input type="datetime-local" name="published_at"
                                        class="form-control @error('published_at') is-invalid @enderror"
                                        value="{{ old('published_at', optional($blog->published_at)->format('Y-m-d\TH:i')) }}">
                                    @error('published_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Status -->
                                <div class="col-12 col-md-6">
                                    <label class="form-label text-muted">Status</label>
                                    <select name="status" class="form-select">
                                        <option value="1" {{ old('status', $blog->status) == 1 ? 'selected' : '' }}>
                                            Active</option>
                                        <option value="0" {{ old('status', $blog->status) == 0 ? 'selected' : '' }}>
                                            Inactive</option>
                                    </select>
                                </div>

                                <!-- Submit -->
                                <div class="col-12 text-end">
                                    <button type="submit" class="btn btn-primary px-4 bdr">
                                        <i class="fa fa-save me-1"></i> Update Blog
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.ckeditor.com/ckeditor5/41.1.0/classic/ckeditor.js"></script>
    <script>
        class MyUploadAdapter {
            constructor(loader) {
                this.loader = loader;
            }

            upload() {
                return this.loader.file.then(file => {
                    return new Promise((resolve, reject) => {
                        const data = new FormData();
                        data.append('upload', file);
                        data.append('_token', '{{ csrf_token() }}');
                        fetch('{{ route('ckeditor.upload') }}', {
                                method: 'POST',
                                body: data
                            })
                            .then(response => response.json())
                            .then(result => resolve({
                                default: result.url
                            }))
                            .catch(error => reject(error.message));
                    });
                });
            }

            abort() {}
        }

        function CustomUploadAdapterPlugin(editor) {
            editor.plugins.get('FileRepository').createUploadAdapter = loader => new MyUploadAdapter(loader);
        }

        ClassicEditor
            .create(document.querySelector('#description'), {
                extraPlugins: [CustomUploadAdapterPlugin],
                toolbar: [
                    'heading', '|',
                    'bold', 'italic', 'bulletedList', 'numberedList', '|',
                    'blockQuote', 'insertTable', 'undo', 'redo', 'imageUpload'
                ]
            })
            .catch(error => {
                console.error(error);
            });

        function previewImage(inputId, previewId, removeBtnId) {
            const input = document.getElementById(inputId);
            const preview = document.getElementById(previewId);
            const removeBtn = document.getElementById(removeBtnId);

            input?.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.src = e.target.result;
                        preview.classList.remove('d-none');
                        removeBtn.classList.remove('d-none');
                    }
                    reader.readAsDataURL(file);
                }
            });

            removeBtn?.addEventListener('click', function() {
                input.value = '';
                preview.src = '';
                preview.classList.add('d-none');
                removeBtn.classList.add('d-none');
            });
        }

        document.addEventListener("DOMContentLoaded", function() {
            previewImage('feature_image', 'featureImagePreview', 'remove_feature_image');
            previewImage('image', 'additionalImagePreview', 'remove_image');
            previewImage('author_image', 'authorImagePreview', 'remove_author_image');
        });
    </script>
@endsection
